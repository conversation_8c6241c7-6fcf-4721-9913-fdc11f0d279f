["tests/test_attribute_api.py::test_create_list_item", "tests/test_attribute_api.py::test_delete_list_item", "tests/test_attribute_api.py::test_delete_list_item_not_found", "tests/test_attribute_api.py::test_get_all_list_names", "tests/test_attribute_api.py::test_get_attribute_list_items", "tests/test_attribute_api.py::test_get_attribute_list_items_with_filters", "tests/test_attribute_api.py::test_get_list_item", "tests/test_attribute_api.py::test_get_list_item_not_found", "tests/test_attribute_api.py::test_patch_list_item", "tests/test_attribute_api.py::test_update_list_item", "tests/test_attribute_api.py::test_update_list_item_not_found", "tests/test_user.py::test_assign_role_to_user", "tests/test_user.py::test_create_duplicate_user", "tests/test_user.py::test_create_role", "tests/test_user.py::test_create_user", "tests/test_user.py::test_delete_user", "tests/test_user.py::test_get_role", "tests/test_user.py::test_get_role_not_found", "tests/test_user.py::test_get_roles", "tests/test_user.py::test_get_user", "tests/test_user.py::test_get_user_not_found", "tests/test_user.py::test_get_user_roles", "tests/test_user.py::test_get_users", "tests/test_user.py::test_get_users_list", "tests/test_user.py::test_get_users_with_filters", "tests/test_user.py::test_remove_nonexistent_role_from_user", "tests/test_user.py::test_remove_role_from_user", "tests/test_user.py::test_update_user"]
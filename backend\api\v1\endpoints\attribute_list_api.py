from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session
from typing import List
from services.attribute_list import AttributeListItemService
from schema.attribute_list_schema import (
    AttributeListItemCreate,
    AttributeListItemUpdate,
    AttributeListItemResponse,
    PaginatedAttributeListsResponse,
)
from api.v1.deps import get_db, Transaction
from api.v1.wrappers import retry_request

router = APIRouter()

attribute_list_service = AttributeListItemService()


@router.get("/", response_model=PaginatedAttributeListsResponse)
@retry_request(max_retries=5, delay=5)
def get_attribute_list_items(
    limit: int = 20,
    page: int = 1,
    list_name: str = "",
    search_query: str = "",
    db: Session = Depends(get_db),
):
    with Transaction(db) as session:
        attribute_list, totalCount = attribute_list_service.get_list_items(
            session.db,
            limit=limit,
            page=page,
            list_name=list_name,
            search_query=search_query if search_query.strip() else None,
        )
    return {"attributeList": attribute_list, "totalCount": totalCount}


@router.get("/list-names/", response_model=List[str])
@retry_request(max_retries=5, delay=5)
def get_all_list_names(db: Session = Depends(get_db)):
    """Get all unique list names"""
    with Transaction(db) as session:
        return attribute_list_service.get_all_list_names(session.db)


@router.get("/{rec_id}", response_model=AttributeListItemResponse)
@retry_request(max_retries=5, delay=5)
def get_list_item(rec_id: int, db: Session = Depends(get_db)):
    with Transaction(db) as session:
        obj = attribute_list_service.get(session.db, rec_id)
        if not obj:
            raise HTTPException(status_code=404, detail="List item not found")
        return obj


@router.post(
    "/", response_model=AttributeListItemResponse, status_code=status.HTTP_201_CREATED
)
@retry_request(max_retries=5, delay=5)
def create_list_item(list_item: AttributeListItemCreate, db: Session = Depends(get_db)):
    with Transaction(db) as session:
        try:
            return attribute_list_service.create(session.db, list_item)
        except HTTPException:
            # Re-raise HTTP exceptions from service layer
            raise


@router.put("/{rec_id}", response_model=AttributeListItemResponse)
@retry_request(max_retries=5, delay=5)
def update_list_item(
    rec_id: int, list_item: AttributeListItemUpdate, db: Session = Depends(get_db)
):
    with Transaction(db) as session:
        try:
            return attribute_list_service.update(session.db, rec_id, list_item)
        except HTTPException:
            # Re-raise HTTP exceptions from service layer
            raise


@router.patch("/{rec_id}", response_model=AttributeListItemResponse)
@retry_request(max_retries=5, delay=5)
def patch_list_item(
    rec_id: int, list_item: AttributeListItemUpdate, db: Session = Depends(get_db)
):
    with Transaction(db) as session:
        return attribute_list_service.update(session.db, rec_id, list_item)


@router.delete("/{rec_id}")
@retry_request(max_retries=5, delay=5)
def delete_list_item(rec_id: int, db: Session = Depends(get_db)):
    with Transaction(db) as session:
        attribute_list_service.delete(session.db, rec_id)
        return {"message": "List item deleted successfully"}
